# Point of Sale Custom Receipt Reset

This Odoo 17 addon provides custom receipt template modifications for the Point of Sale application using proper inheritance patterns.

## Features

- **Custom Receipt Header**: Enhanced company logo display with custom branding
- **Improved Contact Information**: Contact details with icons and better formatting
- **Enhanced Cashier Section**: Improved cashier information display with icons
- **Custom Total Section**: Highlighted total amount with custom styling
- **Custom Footer**: Thank you message and custom branding
- **Responsive Design**: Optimized for different receipt printer widths

## Installation

1. **Copy the addon** to your Odoo custom addons directory:
   ```bash
   cp -r point_of_sale_custom_reset /path/to/odoo/custom_addons/
   ```

2. **Activate Developer Mode** in Odoo:
   - Go to Settings → Activate Developer Mode

3. **Update Apps List**:
   - Go to Apps → Update Apps List

4. **Install the addon**:
   - Search for "Point of Sale Custom Receipt Reset"
   - Click Install

## Usage

Once installed, the custom receipt templates will automatically be applied to all Point of Sale receipts. The changes include:

### Header Customizations
- Enhanced company logo display
- Custom branding tagline
- Improved contact information layout with icons
- Enhanced cashier information display

### Receipt Body Customizations
- Custom total section with highlighting
- Improved formatting and spacing

### Footer Customizations
- Thank you message
- Custom powered-by section

## Customization

To further customize the receipt templates:

1. **Modify Templates**: Edit the XML files in `static/src/app/screens/receipt_screen/receipt/`
2. **Update Styling**: Modify the CSS file in `static/src/css/custom_pos_receipts.css`
3. **Add New Elements**: Use XPath expressions to add new sections to the receipt

### Template Structure

- **Main Receipt**: `custom_order_receipt.xml` - Inherits from `point_of_sale.OrderReceipt`
- **Receipt Header**: `custom_receipt_header.xml` - Inherits from `point_of_sale.ReceiptHeader`
- **Styling**: `custom_pos_receipts.css` - Custom CSS for receipt appearance

### Inheritance Pattern

The addon uses Odoo's template inheritance system:

```xml
<t t-name="point_of_sale.OrderReceipt" t-inherit="point_of_sale.OrderReceipt" t-inherit-mode="extension">
    <xpath expr="//target-element" position="replace|before|after|inside">
        <!-- Custom content -->
    </xpath>
</t>
```

## Technical Details

- **Odoo Version**: 17.0
- **Dependencies**: point_of_sale
- **License**: LGPL-3

## File Structure

```
point_of_sale_custom_reset/
├── __init__.py
├── __manifest__.py
├── README.md
└── static/
    └── src/
        ├── app/
        │   └── screens/
        │       └── receipt_screen/
        │           └── receipt/
        │               ├── custom_order_receipt.xml
        │               └── receipt_header/
        │                   └── custom_receipt_header.xml
        └── css/
            └── custom_pos_receipts.css
```

## Support

For support and customization requests, please contact your development team.

## Contributing

When making changes to this addon:

1. Test thoroughly in a development environment
2. Ensure compatibility with existing PoS functionality
3. Follow Odoo development best practices
4. Document any new features or changes

## License

This addon is licensed under LGPL-3.
