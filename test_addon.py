#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for Point of Sale Custom Receipt Reset addon
This script validates the addon structure and files
"""

import os
import sys
import xml.etree.ElementTree as ET
import json

def test_addon_structure():
    """Test if all required files exist"""
    print("Testing addon structure...")
    
    required_files = [
        '__manifest__.py',
        '__init__.py',
        'README.md',
        'static/src/app/screens/receipt_screen/receipt/custom_order_receipt.xml',
        'static/src/app/screens/receipt_screen/receipt/receipt_header/custom_receipt_header.xml',
        'static/src/css/custom_pos_receipts.css'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    else:
        print("✅ All required files exist")
        return True

def test_manifest():
    """Test manifest file validity"""
    print("Testing manifest file...")
    
    try:
        with open('__manifest__.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Basic validation
        if 'name' not in content:
            print("❌ Manifest missing 'name' field")
            return False
        
        if 'depends' not in content:
            print("❌ Manifest missing 'depends' field")
            return False
        
        if 'point_of_sale' not in content:
            print("❌ Manifest missing point_of_sale dependency")
            return False
        
        print("✅ Manifest file is valid")
        return True
        
    except Exception as e:
        print(f"❌ Error reading manifest: {e}")
        return False

def test_xml_files():
    """Test XML template files validity"""
    print("Testing XML template files...")
    
    xml_files = [
        'static/src/app/screens/receipt_screen/receipt/custom_order_receipt.xml',
        'static/src/app/screens/receipt_screen/receipt/receipt_header/custom_receipt_header.xml'
    ]
    
    for xml_file in xml_files:
        try:
            ET.parse(xml_file)
            print(f"✅ {xml_file} is valid XML")
        except ET.ParseError as e:
            print(f"❌ {xml_file} has XML syntax error: {e}")
            return False
        except Exception as e:
            print(f"❌ Error reading {xml_file}: {e}")
            return False
    
    return True

def test_css_file():
    """Test CSS file exists and has content"""
    print("Testing CSS file...")
    
    css_file = 'static/src/css/custom_pos_receipts.css'
    
    try:
        with open(css_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if len(content.strip()) == 0:
            print("❌ CSS file is empty")
            return False
        
        # Check for some expected CSS classes
        expected_classes = [
            '.custom-receipt-container',
            '.custom-receipt-header',
            '.custom-totals-section'
        ]
        
        for css_class in expected_classes:
            if css_class not in content:
                print(f"❌ CSS missing expected class: {css_class}")
                return False
        
        print("✅ CSS file is valid and contains expected classes")
        return True
        
    except Exception as e:
        print(f"❌ Error reading CSS file: {e}")
        return False

def test_template_inheritance():
    """Test if templates use proper inheritance"""
    print("Testing template inheritance...")
    
    xml_file = 'static/src/app/screens/receipt_screen/receipt/custom_order_receipt.xml'
    
    try:
        tree = ET.parse(xml_file)
        root = tree.getroot()
        
        # Look for inheritance attributes
        inheritance_found = False
        for elem in root.iter():
            if elem.get('t-inherit') and elem.get('t-inherit-mode'):
                inheritance_found = True
                break
        
        if not inheritance_found:
            print("❌ No template inheritance found")
            return False
        
        print("✅ Template inheritance is properly configured")
        return True
        
    except Exception as e:
        print(f"❌ Error checking template inheritance: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Point of Sale Custom Receipt Reset Addon")
    print("=" * 50)
    
    tests = [
        test_addon_structure,
        test_manifest,
        test_xml_files,
        test_css_file,
        test_template_inheritance
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Addon is ready for installation.")
        return 0
    else:
        print("❌ Some tests failed. Please fix the issues before installing.")
        return 1

if __name__ == '__main__':
    sys.exit(main())
