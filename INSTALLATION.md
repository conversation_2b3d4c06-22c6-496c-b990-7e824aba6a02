# Saudi Arabian Thermal Receipt - Installation and Testing Guide

## Prerequisites

1. **Odoo 17 Installation**: Ensure you have Odoo 17 properly installed
2. **Virtual Environment**: Activate your odoo17 virtual environment:
   ```bash
   workon odoo17
   ```
3. **Point of Sale Module**: Ensure the base `point_of_sale` module is installed

## Installation Steps

### Step 1: Validate Addon Structure
Run the validation script to ensure all files are correct:
```bash
cd /home/<USER>/odooProjects/odoo17/custom_addons_hesabat/point_of_sale_custom_reset
python3 test_addon.py
```

You should see: "🎉 All tests passed! Addon is ready for installation."

### Step 2: Install the Addon in Odoo

1. **Start Odoo Server** (if not already running):
   ```bash
   cd /home/<USER>/odooProjects/odoo17
   ./odoo-bin -c config/odoo.conf --addons-path=odoo/addons,custom_addons,custom_addons_hesabat
   ```

2. **Access Odoo Web Interface**:
   - Open your browser and go to your Odoo instance (usually http://localhost:8069)
   - Login with administrator credentials

3. **Enable Developer Mode**:
   - Go to Settings → Activate Developer Mode

4. **Update Apps List**:
   - Go to Apps → Update Apps List
   - Wait for the update to complete

5. **Install the Custom Addon**:
   - In Apps, remove the "Apps" filter and search for "Point of Sale Custom Receipt Reset"
   - Click "Install" on the addon

### Step 3: Configure Point of Sale

1. **Access Point of Sale Settings**:
   - Go to Point of Sale → Configuration → Point of Sale

2. **Create or Edit a PoS Configuration**:
   - Create a new PoS configuration or edit an existing one
   - Ensure the configuration is properly set up with products, payment methods, etc.

3. **Open PoS Session**:
   - Go to Point of Sale → Dashboard
   - Click "New Session" or "Resume" on your PoS configuration

## Testing the Custom Receipt

### Test 1: Basic Receipt Generation

1. **Create a Test Sale**:
   - Open the PoS interface
   - Add some products to the cart
   - Complete the payment
   - Print or preview the receipt

2. **Verify Custom Elements**:
   - Check that the receipt shows the custom header with business registration info
   - Verify Arabic and English labels are displayed
   - Confirm the enhanced totals section with VAT information
   - Check the custom footer with thank you message

### Test 2: Receipt Content Validation

The Saudi Arabian thermal receipt should include:

**Header Section:**
- ✅ Company logo (centered, thermal printer optimized)
- ✅ Business name in Arabic and English (bold, largest font)
- ✅ Complete business address (Arabic primary, English secondary)
- ✅ Commercial Registration (CR) number with Arabic "السجل التجاري" and English "CR No:"
- ✅ VAT Registration number with Arabic "الرقم الضريبي" and English "VAT No:"

**Invoice Information Section:**
- ✅ Invoice type: "فاتورة ضريبية مبسطة / Simplified Tax Invoice" (centered, bold)
- ✅ Invoice number with Arabic "رقم الفاتورة" and English "Invoice No:"
- ✅ Date and time with Arabic "التاريخ والوقت" and English "Date & Time:"
- ✅ Cashier name with Arabic "أمين الصندوق" and English "Cashier:"

**Customer Information (if available):**
- ✅ Customer name with Arabic "اسم العميل" and English "Customer:"
- ✅ Customer VAT with Arabic "الرقم الضريبي للعميل" and English "Customer VAT:"

**Product Table Section:**
- ✅ Bilingual column headers: Product Name, Unit Price, Qty, Total
- ✅ Arabic product names (if available) with English fallback
- ✅ SAR currency symbol (ر.س) for all amounts
- ✅ Customer notes and lot/serial numbers support
- ✅ Subtotal line with separator

**Totals Section:**
- ✅ Subtotal with Arabic "المجموع الفرعي" and English "Subtotal:"
- ✅ Total discount (if applicable) with Arabic "إجمالي الخصم" and English "Total Discount:"
- ✅ Total before VAT with Arabic "المجموع قبل الضريبة" and English "Total before VAT:"
- ✅ VAT 15% with Arabic "ضريبة القيمة المضافة 15%" and English "VAT 15%:"
- ✅ Grand total with Arabic "المجموع الإجمالي" and English "Grand Total:" (bold, highlighted)
- ✅ Total quantity with Arabic "إجمالي الكمية" and English "Total Quantity:"

**Payment Section:**
- ✅ Payment methods header with Arabic "طرق الدفع" and English "Payment Methods:"
- ✅ Cash payment with Arabic "نقداً" and English "Cash:"
- ✅ Card payment with Arabic "بطاقة" and English "Card:"
- ✅ Change amount with Arabic "الباقي" and English "Change:"

**Footer Section:**
- ✅ QR code label: Arabic "امسح الرمز للحصول على الفاتورة الإلكترونية" and English "Scan for E-Invoice"
- ✅ QR code (centered, 100x100px minimum)
- ✅ Thank you message: Arabic "شكراً لزيارتكم" and English "Thank you for your visit"
- ✅ Store contact information (phone, website)

### Test 3: Different Scenarios

Test the receipt with:
- ✅ Single product purchase
- ✅ Multiple products with different quantities
- ✅ Products with taxes
- ✅ Cash payments
- ✅ Card payments
- ✅ Mixed payment methods
- ✅ Transactions with change

## Troubleshooting

### Common Issues

1. **Addon Not Appearing in Apps List**:
   - Ensure the addon is in the correct directory
   - Update the apps list again
   - Check Odoo logs for any errors

2. **Receipt Not Showing Custom Template**:
   - Clear browser cache
   - Restart Odoo server
   - Check that assets are properly loaded

3. **XML/CSS Not Loading**:
   - Verify file paths in `__manifest__.py`
   - Check for syntax errors in XML/CSS files
   - Restart Odoo server after changes

4. **Arabic Text Not Displaying Correctly**:
   - Ensure browser supports Arabic fonts
   - Check CSS font-family settings
   - Verify UTF-8 encoding

### Debugging Steps

1. **Check Odoo Logs**:
   ```bash
   tail -f /var/log/odoo/odoo.log
   ```

2. **Browser Developer Tools**:
   - Open browser developer tools (F12)
   - Check Console for JavaScript errors
   - Verify CSS is loading in Network tab

3. **Template Debugging**:
   - Enable developer mode in Odoo
   - Use browser inspector to check HTML structure
   - Verify template inheritance is working

## Customization

To further customize the receipt:

1. **Modify Templates**: Edit XML files in `static/src/app/screens/receipt_screen/receipt/`
2. **Update Styling**: Modify `static/src/css/custom_pos_receipts.css`
3. **Add New Fields**: Use XPath expressions to add new sections

After making changes:
1. Restart Odoo server
2. Clear browser cache
3. Test the changes

## Support

For issues or customization requests:
1. Check the troubleshooting section above
2. Review Odoo logs for error messages
3. Test in a development environment first
4. Contact your development team for advanced customizations
